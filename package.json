{"name": "nextjs", "version": "0.1.0", "private": true, "engines": {"node": "22.x"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p $PORT", "lint": "next lint", "heroku-postbuild": "npm run build", "analyze": "ANALYZE=true next build", "test": "jest", "vercel": "vercel dev"}, "dependencies": {"@ffmpeg/core": "^0.12.6", "@ffmpeg/ffmpeg": "^0.12.10", "@ffmpeg/util": "^0.12.1", "@hookform/resolvers": "^3.9.0", "@monaco-editor/react": "^4.7.0", "@next/swc-wasm-nodejs": "13.5.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@types/html2canvas": "^0.5.35", "@types/js-cookie": "^3.0.6", "@types/jspdf": "^1.3.3", "@types/lodash": "^4.17.13", "@types/papaparse": "^5.3.15", "@types/sharp": "^0.31.1", "@types/uuid": "^10.0.0", "@types/zxcvbn": "^4.4.5", "@upstash/ratelimit": "^2.0.4", "@upstash/redis": "^1.34.3", "@vercel/analytics": "^1.2.2", "@vercel/node": "^2.3.0", "@vercel/speed-insights": "^1.0.10", "axios": "^1.9.0", "backblaze-b2": "^1.7.0", "browser-image-compression": "^2.0.2", "chart.js": "^4.4.6", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "color-namer": "^1.4.0", "critters": "^0.0.20", "date-fns": "^3.6.0", "dompurify": "^3.2.5", "embla-carousel-react": "^8.3.0", "emoji-picker-react": "^4.12.0", "esbuild": "^0.25.3", "exceljs": "^4.4.0", "execa": "^9.5.1", "firebase": "^11.0.2", "firebase-admin": "^13.4.0", "fluent-ffmpeg": "^2.1.3", "framer-motion": "^11.11.17", "html2canvas": "^1.4.1", "image-js": "^0.37.0", "js-beautify": "^1.15.4", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.446.0", "monaco-editor": "^0.52.0", "monaco-themes": "^0.4.4", "newrelic": "^12.25.0", "next": "^15.3.1", "next-themes": "^0.3.0", "nodemailer": "^6.9.16", "papaparse": "^5.4.1", "path-to-regexp": "^8.2.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "3.4.120", "prettier": "^3.6.2", "qrcode.react": "^4.1.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "recharts": "^2.12.7", "sharp": "^0.33.5", "sonner": "^1.5.0", "sql-formatter": "^15.6.6", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "undici": "^7.8.0", "uuid": "^11.0.3", "vaul": "^0.9.9", "xlsx": "^0.18.5", "xml-formatter": "^3.6.6", "ytdl-core": "^4.11.5", "zod": "^3.23.8", "zxcvbn": "^4.4.2"}, "devDependencies": {"@next/bundle-analyzer": "^13.5.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/fluent-ffmpeg": "^2.1.27", "@types/jest": "^29.5.14", "@types/node": "^20.17.10", "@types/react": "^18.3.16", "@types/react-color": "^3.0.12", "@types/react-dom": "^18.2.18", "@web-std/file": "^3.0.3", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "firebase-tools": "^13.27.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "null-loader": "^4.0.1", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "ts-jest": "^29.3.4", "typescript": "^5.3.3", "vercel": "^25.2.0"}}